/* V2 Finances Styling */

/* Tab Navigation */
.v2-finances-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-gray);
}

.v2-tab-button {
    background: var(--light-gray);
    border: none;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-gray);
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.v2-tab-button:hover {
    background: var(--light-lavender);
    color: var(--primary-navy);
}

.v2-tab-button.active {
    background: var(--light-white);
    color: var(--primary-purple);
    border-bottom-color: var(--primary-purple);
}

/* Tab Content */
.v2-tab-content {
    position: relative;
}

.v2-tab-pane {
    display: none;
}

.v2-tab-pane.active {
    display: block;
}

/* Finances Content */
.v2-finances-content {
    padding: 20px 0;
}

.v2-finances-header {
    margin-bottom: 25px;
}

.v2-finances-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0;
}

.v2-finances-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Form Section */
.v2-finances-form-section {
    background: var(--light-white);
}

.v2-finances-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.v2-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.v2-form-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-navy);
}

.v2-form-select,
.v2-form-input {
    padding: 12px 16px;
    border: 1px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    background: var(--light-white);
    transition: border-color 0.3s ease;
}

.v2-form-select:focus,
.v2-form-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
}

/* Recipient Section */
.v2-recipient-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.v2-recipient-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.v2-recipient-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-navy);
}

.v2-amount-input {
    position: relative;
    display: flex;
    align-items: center;
}

.v2-currency-symbol {
    position: absolute;
    left: 12px;
    font-weight: 600;
    color: var(--text-gray);
    z-index: 1;
}

.v2-amount-input .v2-form-input {
    padding-left: 30px;
}

.v2-total-section {
    padding: 15px;
    background: var(--light-lavender);
    border-radius: 6px;
    text-align: center;
    font-size: 16px;
    color: var(--primary-navy);
}

/* Info Section */
.v2-finances-info-section {
    background: var(--light-white);
}

.v2-info-card {
    background: var(--light-lavender);
    border-radius: 8px;
    padding: 25px;
}

.v2-info-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0 0 15px 0;
}

.v2-info-text {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.v2-info-text:last-child {
    margin-bottom: 0;
}

.v2-info-highlight {
    background: var(--light-white);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 15px 0;
    font-size: 14px;
    color: var(--primary-navy);
}

.v2-powered-by {
    margin-top: 20px;
    text-align: center;
}

.v2-stripe-logo {
    max-height: 30px;
    opacity: 0.7;
}

/* Buttons */
.v2-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.v2-btn-primary {
    background: var(--primary-purple);
    color: var(--light-white);
}

.v2-btn-primary:hover {
    background: #4a3d91;
    transform: translateY(-1px);
}

.v2-btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.v2-form-actions {
    margin-top: 20px;
}

/* Links */
.v2-link {
    color: var(--primary-purple);
    text-decoration: none;
}

.v2-link:hover {
    text-decoration: underline;
}

/* Intro Text */
.v2-intro-text {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-gray);
    margin-bottom: 25px;
}

/* UK Specific Styles */
.v2-uk-intro {
    margin-bottom: 30px;
}

.v2-uk-donation-widget {
    background: var(--light-white);
    border: 1px solid var(--border-gray);
    border-radius: 8px;
    overflow: hidden;
}

.v2-donation-steps {
    padding: 25px;
}

.v2-step-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.v2-step-indicator {
    display: flex;
    gap: 8px;
}

.v2-step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--border-gray);
    color: var(--text-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.v2-step-number.active {
    background: var(--primary-purple);
    color: var(--light-white);
}

.v2-step-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0;
}

.v2-step-lock {
    color: var(--success-green);
}

.v2-donation-frequency {
    margin-bottom: 25px;
}

.v2-frequency-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 10px;
}

.v2-frequency-tab {
    flex: 1;
    padding: 10px 16px;
    background: var(--light-gray);
    border: none;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-gray);
    cursor: pointer;
    transition: all 0.3s ease;
}

.v2-frequency-tab.active {
    background: var(--primary-purple);
    color: var(--light-white);
}

.v2-frequency-note {
    font-size: 12px;
    color: var(--text-gray);
    margin: 0;
}

.v2-amount-selection {
    margin-bottom: 25px;
}

.v2-amount-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.v2-amount-btn {
    padding: 12px;
    background: var(--light-white);
    border: 2px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-navy);
    cursor: pointer;
    transition: all 0.3s ease;
}

.v2-amount-btn:hover,
.v2-amount-btn.selected {
    border-color: var(--primary-purple);
    background: var(--light-lavender);
}

.v2-custom-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
}

.v2-group-selection {
    margin-bottom: 25px;
}

.v2-dropdown-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 8px;
}

.v2-group-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    background: var(--light-white);
}

/* Gift Aid Section */
.v2-gift-aid {
    background: var(--light-lavender);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.v2-gift-aid-logo {
    text-align: center;
    margin-bottom: 15px;
}

.v2-gift-aid-text {
    background: var(--primary-purple);
    color: var(--light-white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    font-style: italic;
}

.v2-gift-aid-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-navy);
    margin-bottom: 15px;
    text-align: center;
}

.v2-checkbox-group {
    margin-bottom: 15px;
}

.v2-checkbox {
    margin-right: 10px;
}

.v2-checkbox-label {
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-gray);
}

.v2-gift-aid-disclaimer {
    font-size: 11px;
    color: var(--text-gray);
    line-height: 1.3;
    margin: 0;
}

.v2-donorbox-logo {
    color: var(--primary-purple);
    font-weight: 600;
}

.v2-powered-text {
    color: var(--text-gray);
    font-size: 12px;
    margin-right: 5px;
}

/* Alert Styles */
.v2-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.v2-alert-success {
    background: #d1fae5;
    border: 1px solid #10b981;
    color: #065f46;
}

.v2-alert-error {
    background: #fee2e2;
    border: 1px solid #ef4444;
    color: #991b1b;
}

.v2-alert ul {
    margin: 0;
    padding-left: 20px;
}

/* Loading States */
.v2-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.v2-btn.loading {
    position: relative;
    color: transparent;
}

.v2-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Focus States */
.v2-tab-button:focus,
.v2-btn:focus,
.v2-form-select:focus,
.v2-form-input:focus {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .v2-finances-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .v2-finances-tabs {
        flex-direction: column;
        gap: 0;
    }

    .v2-tab-button {
        border-radius: 0;
        border-bottom: 1px solid var(--border-gray);
    }

    .v2-tab-button.active {
        border-bottom-color: var(--primary-purple);
    }

    .v2-amount-buttons {
        grid-template-columns: 1fr;
    }

    .v2-step-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .v2-finances-title {
        font-size: 20px;
    }

    .v2-info-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .v2-account-container {
        padding: 15px;
    }

    .v2-finances-content {
        padding: 15px 0;
    }

    .v2-tab-button {
        padding: 10px 16px;
        font-size: 13px;
    }

    .v2-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .v2-btn-large {
        padding: 14px 28px;
        font-size: 15px;
    }
}
