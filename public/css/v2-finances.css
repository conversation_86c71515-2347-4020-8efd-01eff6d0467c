/* V2 Finances Styling */

/* Tab Navigation */
.v2-finances-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-gray);
}

.v2-tab-button {
    background: var(--light-gray);
    border: none;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-gray);
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.v2-tab-button:hover {
    background: var(--light-lavender);
    color: var(--primary-navy);
}

.v2-tab-button.active {
    background: var(--light-white);
    color: var(--primary-purple);
    border-bottom-color: var(--primary-purple);
}

/* Tab Content */
.v2-tab-content {
    position: relative;
}

.v2-tab-pane {
    display: none;
}

.v2-tab-pane.active {
    display: block;
}

/* Finances Content */
.v2-finances-content {
    padding: 20px 0;
}

.v2-finances-header {
    margin-bottom: 25px;
}

.v2-finances-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0;
}

.v2-finances-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Form Section */
.v2-finances-form-section {
    background: var(--light-white);
}

.v2-finances-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.v2-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.v2-form-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-navy);
}

.v2-form-select,
.v2-form-input {
    padding: 12px 16px;
    border: 1px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    background: var(--light-white);
    transition: border-color 0.3s ease;
}

.v2-form-select:focus,
.v2-form-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
}

/* Recipient Section */
.v2-recipient-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.v2-recipient-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.v2-recipient-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-navy);
}

.v2-amount-input {
    position: relative;
    display: flex;
    align-items: center;
}

.v2-currency-symbol {
    position: absolute;
    left: 12px;
    font-weight: 600;
    color: var(--text-gray);
    z-index: 1;
}

.v2-amount-input .v2-form-input {
    padding-left: 30px;
}

.v2-total-section {
    padding: 15px;
    background: var(--light-lavender);
    border-radius: 6px;
    text-align: center;
    font-size: 16px;
    color: var(--primary-navy);
}

/* Info Section */
.v2-finances-info-section {
    background: var(--light-white);
}

.v2-info-card {
    background: var(--light-lavender);
    border-radius: 8px;
    padding: 25px;
}

.v2-info-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0 0 15px 0;
}

.v2-info-text {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.v2-info-text:last-child {
    margin-bottom: 0;
}

.v2-info-highlight {
    background: var(--light-white);
    padding: 12px 16px;
    border-radius: 6px;
    margin: 15px 0;
    font-size: 14px;
    color: var(--primary-navy);
}

.v2-powered-by {
    margin-top: 20px;
    text-align: center;
}

.v2-stripe-logo {
    max-height: 30px;
    opacity: 0.7;
}

/* Buttons */
.v2-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.v2-btn-primary {
    background: var(--primary-purple);
    color: var(--light-white);
}

.v2-btn-primary:hover {
    background: #4a3d91;
    transform: translateY(-1px);
}

.v2-btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.v2-form-actions {
    margin-top: 20px;
}

/* Links */
.v2-link {
    color: var(--primary-purple);
    text-decoration: none;
}

.v2-link:hover {
    text-decoration: underline;
}

/* Intro Text */
.v2-intro-text {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-gray);
    margin-bottom: 25px;
}

/* UK Specific Styles - Donorbox Design */
.v2-uk-intro {
    margin-bottom: 30px;
}

.v2-uk-donation-widget {
    background: var(--light-white);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.v2-donorbox-container {
    padding: 0;
    background: #ffffff;
}

/* Donorbox Header */
.v2-donorbox-header {
    background: #20b2aa;
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.v2-donorbox-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.v2-donorbox-security {
    display: flex;
    align-items: center;
    gap: 12px;
}

.v2-progress-dots {
    display: flex;
    gap: 6px;
}

.v2-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
}

.v2-dot.active {
    background: white;
}

/* Frequency Section */
.v2-frequency-section {
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
}

.v2-frequency-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    overflow: hidden;
}

.v2-frequency-tab {
    flex: 1;
    padding: 10px 16px;
    background: #f9fafb;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.v2-frequency-tab.active {
    background: #20b2aa;
    color: white;
}

.v2-frequency-note {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

.v2-link-text {
    color: #20b2aa;
    text-decoration: underline;
    cursor: pointer;
}

/* Amount Section */
.v2-amount-section {
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
}

.v2-amount-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.v2-amount-btn {
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.v2-amount-btn:hover,
.v2-amount-btn.selected {
    background: #20b2aa;
    color: white;
    border-color: #20b2aa;
}

.v2-custom-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    background: #ffffff;
}

.v2-custom-input:focus {
    outline: none;
    border-color: #20b2aa;
    box-shadow: 0 0 0 3px rgba(32, 178, 170, 0.1);
}

/* Group Section */
.v2-group-section {
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
}

.v2-group-dropdown {
    position: relative;
}

.v2-group-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #20b2aa;
    margin-bottom: 8px;
}

.v2-group-select {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    background: #ffffff;
    appearance: none;
}

.v2-dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    pointer-events: none;
    margin-top: 12px;
}

/* Gift Aid Section */
.v2-gift-aid {
    padding: 20px;
    background: #f9fafb;
}

.v2-gift-aid-header {
    text-align: center;
    margin-bottom: 16px;
}

.v2-gift-aid-logo {
    background: #6366f1;
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    font-style: italic;
    display: inline-block;
}

.v2-gift-aid-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 16px 0;
    text-align: center;
}

.v2-checkbox-container {
    margin-bottom: 12px;
}

.v2-checkbox {
    margin-right: 8px;
    transform: scale(1.1);
}

.v2-checkbox-label {
    font-size: 13px;
    line-height: 1.4;
    color: #374151;
    cursor: pointer;
}

.v2-gift-aid-disclaimer {
    font-size: 11px;
    color: #6b7280;
    line-height: 1.3;
    margin: 0;
}

/* Next Button Section */
.v2-next-section {
    padding: 20px;
    text-align: center;
}

.v2-next-btn {
    background: #20b2aa;
    color: white;
    border: none;
    padding: 14px 32px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.v2-next-btn:hover {
    background: #1a9b94;
}

/* Powered by Section */
.v2-powered-section {
    padding: 16px 20px;
    text-align: center;
    border-top: 1px solid #f3f4f6;
    background: #f9fafb;
}

.v2-powered-text {
    color: #6b7280;
    font-size: 12px;
    margin-right: 4px;
}

.v2-donorbox-brand {
    color: #20b2aa;
    font-weight: 600;
    font-size: 12px;
}

/* UK Info Card */
.v2-uk-info-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
}

.v2-uk-info-title {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 16px 0;
}

.v2-uk-info-content {
    font-size: 14px;
    line-height: 1.5;
}

.v2-uk-info-text {
    color: #475569;
    margin-bottom: 12px;
}

.v2-uk-info-text:last-child {
    margin-bottom: 0;
}

.v2-uk-info-highlight {
    background: #ffffff;
    padding: 12px;
    border-radius: 4px;
    margin: 12px 0;
    border-left: 4px solid #20b2aa;
}

.v2-uk-info-highlight strong {
    color: #1e293b;
}

/* Responsive Design for UK Tab */
@media (max-width: 768px) {
    .v2-donorbox-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .v2-amount-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .v2-frequency-tabs {
        flex-direction: column;
    }

    .v2-frequency-tab {
        border-radius: 0;
    }

    .v2-frequency-tab:first-child {
        border-radius: 4px 4px 0 0;
    }

    .v2-frequency-tab:last-child {
        border-radius: 0 0 4px 4px;
    }
}

@media (max-width: 480px) {
    .v2-donorbox-header {
        padding: 12px 16px;
    }

    .v2-donorbox-title {
        font-size: 16px;
    }

    .v2-frequency-section,
    .v2-amount-section,
    .v2-group-section,
    .v2-gift-aid,
    .v2-next-section {
        padding: 16px;
    }

    .v2-next-btn {
        padding: 12px 24px;
        font-size: 14px;
    }
}

/* Alert Styles */
.v2-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
}

.v2-alert-success {
    background: #d1fae5;
    border: 1px solid #10b981;
    color: #065f46;
}

.v2-alert-error {
    background: #fee2e2;
    border: 1px solid #ef4444;
    color: #991b1b;
}

.v2-alert ul {
    margin: 0;
    padding-left: 20px;
}

/* Loading States */
.v2-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.v2-btn.loading {
    position: relative;
    color: transparent;
}

.v2-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Focus States */
.v2-tab-button:focus,
.v2-btn:focus,
.v2-form-select:focus,
.v2-form-input:focus {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
}

/* Field Error States */
.v2-field-error {
    color: #ef4444;
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

.v2-form-input.error,
.v2-form-select.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Form validation styles */
.v2-form-group.has-error .v2-form-input,
.v2-form-group.has-error .v2-form-select {
    border-color: #ef4444;
}

.v2-form-group.has-error .v2-form-label {
    color: #ef4444;
}

/* Responsive Design */
@media (max-width: 768px) {
    .v2-finances-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .v2-finances-tabs {
        flex-direction: column;
        gap: 0;
    }

    .v2-tab-button {
        border-radius: 0;
        border-bottom: 1px solid var(--border-gray);
    }

    .v2-tab-button.active {
        border-bottom-color: var(--primary-purple);
    }

    .v2-amount-buttons {
        grid-template-columns: 1fr;
    }

    .v2-step-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .v2-finances-title {
        font-size: 20px;
    }

    .v2-info-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .v2-account-container {
        padding: 15px;
    }

    .v2-finances-content {
        padding: 15px 0;
    }

    .v2-tab-button {
        padding: 10px 16px;
        font-size: 13px;
    }

    .v2-btn {
        padding: 10px 20px;
        font-size: 13px;
    }

    .v2-btn-large {
        padding: 14px 28px;
        font-size: 15px;
    }
}
