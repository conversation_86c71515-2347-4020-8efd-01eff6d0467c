<div class="v2-finances-content">
    <div class="v2-finances-header">
        <h2 class="v2-finances-title">Donate - United Kingdom</h2>
    </div>

    <div class="v2-uk-intro">
        <p class="v2-intro-text">
            To make a contribution to Common Change UK and to make a contribution to a specific group account,
            you may either fill out the form below, or follow the instructions to create a Standing Order.
        </p>
    </div>

    <div class="v2-finances-grid">
        <!-- Left Column - Donation Widget -->
        <div class="v2-finances-form-section">
            <div class="v2-uk-donation-widget">
                <!-- Donorbox-style Widget -->
                <div class="v2-donorbox-container">
                    <!-- Header with progress and lock -->
                    <div class="v2-donorbox-header">
                        <div class="v2-donorbox-title">Choose amount</div>
                        <div class="v2-donorbox-security">
                            <i class="fas fa-lock"></i>
                            <div class="v2-progress-dots">
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot"></span>
                                <span class="v2-dot active"></span>
                            </div>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>

                    <!-- Frequency Selection -->
                    <div class="v2-frequency-section">
                        <div class="v2-frequency-tabs">
                            <button class="v2-frequency-tab active" data-frequency="one-time">One-time</button>
                            <button class="v2-frequency-tab" data-frequency="monthly">Monthly</button>
                        </div>

                        <p class="v2-frequency-note">
                            You can log in to <span class="v2-link-text">edit your recurring donation</span> any time
                            <i class="fas fa-info-circle" title="More information"></i>
                        </p>
                    </div>

                    <!-- Amount Selection -->
                    <div class="v2-amount-section">
                        <div class="v2-amount-buttons">
                            <button class="v2-amount-btn" data-amount="20">£ 20</button>
                            <button class="v2-amount-btn" data-amount="50">£ 50</button>
                            <button class="v2-amount-btn" data-amount="100">£ 100</button>
                        </div>

                        <div class="v2-custom-amount">
                            <input type="number" class="v2-custom-input" placeholder="£ Custom Amount" min="1" step="0.01">
                        </div>
                    </div>

                    <!-- Group Selection -->
                    <div class="v2-group-section">
                        <div class="v2-group-dropdown">
                            <label class="v2-group-label">Donate to This Group</label>
                            <select class="v2-group-select">
                                <option value="">Choose a group</option>
                                <!-- Groups will be populated dynamically -->
                            </select>
                            <i class="fas fa-chevron-down v2-dropdown-arrow"></i>
                        </div>
                    </div>

                    <!-- Gift Aid Section -->
                    <div class="v2-gift-aid">
                        <div class="v2-gift-aid-header">
                            <div class="v2-gift-aid-logo">gift aid it</div>
                        </div>

                        <div class="v2-gift-aid-content">
                            <h4 class="v2-gift-aid-title">Boost your donations by 25% at no cost to you!</h4>

                            <div class="v2-checkbox-container">
                                <input type="checkbox" id="gift-aid-checkbox" class="v2-checkbox">
                                <label for="gift-aid-checkbox" class="v2-checkbox-label">
                                    Yes, I am a UK taxpayer and I would like Common Change UK to
                                    <span class="v2-link-text">reclaim the tax on all qualifying donations I have made</span>,
                                    as well as any future donations, until I notify them otherwise.
                                </label>
                            </div>

                            <p class="v2-gift-aid-disclaimer">
                                I understand that if I pay less Income Tax and/or Capital Gains tax than the amount of Gift Aid claimed on all my donations in that tax year it is my responsibility to pay any difference.
                            </p>
                        </div>
                    </div>

                    <!-- Next Button -->
                    <div class="v2-next-section">
                        <button type="button" class="v2-next-btn">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <!-- Powered by Donorbox -->
                    <div class="v2-powered-section">
                        <span class="v2-powered-text">Powered by</span>
                        <span class="v2-donorbox-brand">Donorbox</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Information -->
        <div class="v2-finances-info-section">
            <div class="v2-uk-info-card">
                <h3 class="v2-uk-info-title">NOTE</h3>

                <div class="v2-uk-info-content">
                    <p class="v2-uk-info-text">
                        If you have any questions or need support, please contact us at
                        <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>.
                    </p>

                    <p class="v2-uk-info-highlight">
                        <strong>Donations are typically processed within 5 business days.</strong>
                    </p>

                    <p class="v2-uk-info-text">
                        For more info see
                        <a href="https://commonchange.zendesk.com" target="_blank" class="v2-link">https://commonchange.zendesk.com</a><br>
                        Or contact us on
                        <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Frequency tab switching
    const frequencyTabs = document.querySelectorAll('.v2-frequency-tab');
    frequencyTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            frequencyTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Amount button selection
    const amountButtons = document.querySelectorAll('.v2-amount-btn');
    const customInput = document.querySelector('.v2-custom-input');

    amountButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            amountButtons.forEach(b => b.classList.remove('selected'));
            this.classList.add('selected');
            customInput.value = '';

            // Update custom input placeholder with selected amount
            const amount = this.getAttribute('data-amount');
            customInput.placeholder = `£ ${amount}`;
        });
    });

    // Custom amount input
    customInput.addEventListener('input', function() {
        if (this.value) {
            amountButtons.forEach(b => b.classList.remove('selected'));
        }
    });

    // Custom amount focus
    customInput.addEventListener('focus', function() {
        amountButtons.forEach(b => b.classList.remove('selected'));
    });

    // Next button functionality
    const nextBtn = document.querySelector('.v2-next-btn');
    nextBtn.addEventListener('click', function() {
        // Get selected values
        const selectedFrequency = document.querySelector('.v2-frequency-tab.active').getAttribute('data-frequency');
        const selectedAmount = document.querySelector('.v2-amount-btn.selected');
        const customAmount = customInput.value;
        const selectedGroup = document.querySelector('.v2-group-select').value;
        const giftAidChecked = document.querySelector('#gift-aid-checkbox').checked;

        // Determine final amount
        let finalAmount = 0;
        if (selectedAmount) {
            finalAmount = selectedAmount.getAttribute('data-amount');
        } else if (customAmount) {
            finalAmount = customAmount;
        }

        if (finalAmount <= 0) {
            alert('Please select or enter a donation amount.');
            return;
        }

        // For now, just show a confirmation (in real implementation, this would proceed to payment)
        let message = `Donation Details:\n`;
        message += `Amount: £${finalAmount}\n`;
        message += `Frequency: ${selectedFrequency}\n`;
        if (selectedGroup) {
            message += `Group: ${selectedGroup}\n`;
        }
        if (giftAidChecked) {
            message += `Gift Aid: Yes\n`;
        }
        message += `\nThis would proceed to payment processing.`;

        alert(message);
    });

    // Initialize placeholder
    customInput.placeholder = '£ Custom Amount';
});
</script>
