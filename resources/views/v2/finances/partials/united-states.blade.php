<div class="v2-finances-content">
    <div class="v2-finances-header">
        <h2 class="v2-finances-title">Donate - United States</h2>
    </div>

    <div class="v2-finances-grid">
        <!-- Left Column - Form -->
        <div class="v2-finances-form-section">
            <form id="us-donation-form" class="v2-finances-form">
                <div class="v2-form-group">
                    <label for="donation-type" class="v2-form-label">Is this a one-time or recurring donation?</label>
                    <select id="donation-type" name="donation_type" class="v2-form-select">
                        <option value="">Select one...</option>
                        <option value="one-time">One-time</option>
                        <option value="recurring">Recurring</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <label for="donation-frequency" class="v2-form-label">Donation frequency</label>
                    <select id="donation-frequency" name="donation_frequency" class="v2-form-select">
                        <option value="">Select one...</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>

                <div class="v2-form-group">
                    <label class="v2-form-label">Recipient</label>
                    
                    <div class="v2-recipient-section">
                        <div class="v2-recipient-item">
                            <label for="amount-commonchange" class="v2-recipient-label">Common Change Operations</label>
                            <div class="v2-amount-input">
                                <span class="v2-currency-symbol">$</span>
                                <input type="number" id="amount-commonchange" name="amount_commonchange" 
                                       class="v2-form-input" placeholder="0.00" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="v2-recipient-item">
                            <label for="amount-group" class="v2-recipient-label">My Group</label>
                            <div class="v2-amount-input">
                                <span class="v2-currency-symbol">$</span>
                                <input type="number" id="amount-group" name="amount_group" 
                                       class="v2-form-input" placeholder="0.00" step="0.01" min="0">
                            </div>
                        </div>

                        <div class="v2-total-section">
                            <strong>Total: $<span id="total-amount">0.00</span></strong>
                        </div>
                    </div>
                </div>

                <div class="v2-form-actions">
                    <button type="submit" class="v2-btn v2-btn-primary">Donate</button>
                </div>
            </form>
        </div>

        <!-- Right Column - Information -->
        <div class="v2-finances-info-section">
            <div class="v2-info-card">
                <h3 class="v2-info-title">How can I change recurring donations?</h3>
                <p class="v2-info-text">
                    If you would like to make a change to your contribution, please contact us at 
                    <a href="mailto:<EMAIL>" class="v2-link"><EMAIL></a>
                </p>
                
                <div class="v2-info-highlight">
                    <strong>Donations are typically processed within 48-72 hours.</strong>
                </div>

                <div class="v2-powered-by">
                    <img src="{{ asset('images/stripe_logo.png') }}" alt="Powered by Stripe" class="v2-stripe-logo">
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total amount
    function calculateTotal() {
        const commonChangeAmount = parseFloat(document.getElementById('amount-commonchange').value) || 0;
        const groupAmount = parseFloat(document.getElementById('amount-group').value) || 0;
        const total = commonChangeAmount + groupAmount;
        document.getElementById('total-amount').textContent = total.toFixed(2);
    }

    // Add event listeners for amount inputs
    document.getElementById('amount-commonchange').addEventListener('input', calculateTotal);
    document.getElementById('amount-group').addEventListener('input', calculateTotal);

    // Form submission
    document.getElementById('us-donation-form').addEventListener('submit', function(e) {
        e.preventDefault();
        // Add form validation and submission logic here
        alert('Donation form submitted (placeholder)');
    });
});
</script>
