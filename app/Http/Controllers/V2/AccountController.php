<?php

namespace App\Http\Controllers\V2;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Address;
use App\Models\StripeMetadata;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;

class AccountController extends Controller
{
    private $stripe;

    public function __construct()
    {
        $stripeSecretKey = env('STRIPE_SECRET_KEY');

        // Check if the Stripe key is set before initializing the client
        if ($stripeSecretKey) {
            $this->stripe = new \Stripe\StripeClient($stripeSecretKey);
        } else {
            // Log the missing key
            \Log::error('Stripe secret key is not set in environment variables');
            // Initialize with empty config to avoid errors when listing routes
            $this->stripe = null;
        }
    }

    /**
     * Display the account index page (redirects to security by default)
     */
    public function index()
    {
        return redirect()->route('v2.account.security');
    }

    /**
     * Display the account security page
     */
    public function security()
    {
        $user = Auth::user();
        return view('v2.accounts.security', compact('user'));
    }

    /**
     * Update account security settings
     */
    public function updateSecurity(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email,' . $user->id,
            'new_password' => 'nullable|min:8|confirmed',
            'profile_pic' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Update email
        $user->email = $request->email;

        // Update password if provided
        if ($request->filled('new_password')) {
            $user->password = $request->new_password;
        }

        // Handle profile picture upload
        if ($request->hasFile('profile_pic')) {
            $file = $request->file('profile_pic');
            if ($file->isValid()) {
                $file2 = $file->move(public_path().'/pix');
                $user->profile_pic = $file2->getFilename();
            }
        }

        $user->save();

        return back()->with('success', 'Security settings updated successfully.');
    }

    /**
     * Display the contact info page
     */
    public function contact()
    {
        $user = Auth::user();
        return view('v2.accounts.contact', compact('user'));
    }

    /**
     * Update contact information
     */
    public function updateContact(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'firstname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'lastname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
            'address.addr1' => 'nullable|string|max:255',
            'address.addr2' => 'nullable|string|max:255',
            'address.city' => 'nullable|string|max:255',
            'address.state' => 'nullable|string|max:255',
            'address.postcode' => 'nullable|string|max:20',
            'address.country' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Update user basic info
        $user->fill($request->only('firstname', 'lastname'));

        // Update address
        $address = $user->address ?: new Address;
        $address->fill($request->input('address', []));

        if ($user->address) {
            $address->save();
        } else {
            $user->address()->save($address);
        }

        $user->save();

        return back()->with('success', 'Contact information updated successfully.');
    }

    /**
     * Display the social media page
     */
    public function social()
    {
        $user = Auth::user();
        return view('v2.accounts.social', compact('user'));
    }

    /**
     * Update social media settings
     */
    public function updateSocial(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'facebook' => 'nullable|url',
            'twitter' => 'nullable|url',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user->fill($request->only('facebook', 'twitter'));
        $user->save();

        return back()->with('success', 'Social media settings updated successfully.');
    }

    /**
     * Display the notifications page
     */
    public function notifications()
    {
        $user = Auth::user();
        return view('v2.accounts.notifications', compact('user'));
    }

    /**
     * Update notification preferences
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'default_email_notification' => 'required|in:daily_digest,daily_per_discussion,per_comment,none',
            'share_email' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user->default_email_notification = $request->default_email_notification;
        $user->share_email = $request->has('share_email');
        $user->save();

        return back()->with('success', 'Notification preferences updated successfully.');
    }

    /**
     * Display the finances page
     */
    public function finances()
    {
        $user = Auth::user();
        $address = $user->address;
        $country_code = $address ? $address->country : null;

        // Determine active tab based on user's country
        $activeTab = 'us'; // default
        switch ($country_code) {
            case 'ZA':
            case 'za':
                $activeTab = 'sa';
                break;
            case 'GB':
            case 'gb':
                $activeTab = 'uk';
                break;
            case 'US':
            case 'us':
            default:
                $activeTab = 'us';
                break;
        }

        return view('v2.finances.index', compact('user', 'activeTab'));
    }

    /**
     * Process donation payment
     */
    public function processDonation(Request $request)
    {
        $rules = [
            'donation_type' => 'required|in:one-time,recurring',
            'donation_frequency' => 'required_if:donation_type,recurring|in:weekly,monthly',
            'amount_commonchange' => 'nullable|numeric|min:0',
            'amount_group' => 'nullable|numeric|min:0',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator->messages());
        }

        $user = Auth::user();
        $token = $request->input('stripeToken');

        // Validate that at least one amount is provided
        $amount_commonchange = (float) $request->input('amount_commonchange', 0);
        $amount_group = (float) $request->input('amount_group', 0);

        if ($amount_commonchange <= 0 && $amount_group <= 0) {
            return back()->withInput()->withErrors(['amount' => 'Please enter a donation amount.']);
        }

        // Validate minimum group contribution
        if ($amount_group > 0 && $amount_group < 3) {
            return back()->withInput()->withErrors(['amount_group' => 'If you\'re making a group contribution, it must be at least $3.00.']);
        }

        if (!isset($token)) {
            return back()->withInput()->withErrors(['payment' => 'Payment token is required.']);
        }

        if (!$this->stripe) {
            return back()->withInput()->withErrors(['payment' => 'Payment processing is currently unavailable.']);
        }

        try {
            $customer = Subscription::get_or_create_customer($user, $token);
            $group = $user->default_group()->first();

            if ($request->input('donation_type') == 'recurring') {
                $period = $request->input('donation_frequency');

                if ($amount_commonchange > 0) {
                    Subscription::set_subscription($user, 'dollar_'.$period.'_cc', $amount_commonchange, StripeMetadata::create()->setCCOps()->toArray());
                } else {
                    Subscription::cancel_subscription($user, 'dollar_'.$period.'_cc');
                }

                if ($amount_group > 0) {
                    Subscription::set_subscription($user, 'dollar_'.$period.'_group', $amount_group, StripeMetadata::create()->setGroup($group)->toArray());
                } else {
                    Subscription::cancel_subscription($user, 'dollar_'.$period.'_group');
                }
            } else {
                // One-time charge
                if ($amount_commonchange > 0) {
                    $charge1 = $this->stripe->charges->create([
                        'amount' => $amount_commonchange * 100, // amount in cents
                        'currency' => 'usd',
                        'customer' => $customer->id,
                        'metadata' => StripeMetadata::create()->setUser($user)->setCCOps()->toArray(),
                    ]);
                }

                if ($amount_group > 0) {
                    $charge2 = $this->stripe->charges->create([
                        'amount' => $amount_group * 100, // amount in cents
                        'currency' => 'usd',
                        'customer' => $customer->id,
                        'metadata' => StripeMetadata::create()->setUser($user)->setGroup($group)->toArray(),
                    ]);
                }
            }

            return back()->with('success', 'Your ' . $request->input('donation_type') . ' donation was successful!');

        } catch (\Stripe\Exception\CardException $e) {
            return back()->withInput()->withErrors(['payment' => 'Your card has been declined: ' . $e->getMessage()]);
        } catch (\Exception $e) {
            \Log::error('Donation processing error: ' . $e->getMessage());
            return back()->withInput()->withErrors(['payment' => 'An error occurred while processing your donation. Please try again.']);
        }
    }
}
